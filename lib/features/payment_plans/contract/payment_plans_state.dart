import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_plans_state.freezed.dart';

enum CurrentAction { purchase, supabaseFailed, restore }

@freezed
sealed class PaymentPlansState with _$PaymentPlansState {
  const factory PaymentPlansState({
    @Default([]) List<SubscriptionPlan> plans,
    required SubscriptionPlan selectedPlan,
    @Default(false) bool isLoading,
    @Default(CurrentAction.purchase) CurrentAction currentAction,
    @Default(false) bool isGuest,
  }) = _PaymentPlansState;
}
