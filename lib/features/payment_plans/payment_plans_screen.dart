import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import '../../localization/generated/l10n.dart';
import 'contract/payment_plans_contract.dart';
import 'payment_plans_presenter.dart';

class PaymentPlansScreen extends ConsumerWidget {
  const PaymentPlansScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(paymentPlansPresenterProvider);
    final presenter = ref.read(paymentPlansPresenterProvider.notifier);
    _handleSideEffects(context, ref);

    final str = Str.of(context);

    return AdMobScaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              child: IntrinsicHeight(
                child: Column(
                  children: [
                    _buildHeader(context, presenter),
                    Expanded(
                      child: Stack(
                        children: [
                          Positioned(
                            bottom: 0,
                            right: 0,
                            left: 0,
                            child: Opacity(
                              opacity: 0.5,
                              child: Image.asset(
                                'assets/images/plans_bg.png',
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Image.asset(
                                  'assets/images/plan_header.png',
                                  height: 24,
                                ),
                                const SizedBox(height: 30),
                                _buildFeaturesList(str),
                                const SizedBox(height: 42),
                                _buildPaymentPlans(state, presenter),
                                const SizedBox(height: 24),
                                if (state.currentAction ==
                                    CurrentAction.supabaseFailed) ...[
                                  PrimaryButton(
                                    onPressed: () => presenter.intentHandler(
                                      const PaymentPlansIntent.restorePressed(),
                                    ),
                                    text: str.restoreYourPurchase,
                                    isLoading: state.isLoading,
                                  ),
                                  const SizedBox(height: 16),
                                ] else ...[
                                  PrimaryButton(
                                    onPressed: () => presenter.intentHandler(
                                      const PaymentPlansIntent
                                          .continuePressed(),
                                    ),
                                    text: str.paymentPlansContinue,
                                    isLoading: state.isLoading,
                                  ),
                                  const SizedBox(height: 16),
                                  state.isGuest
                                      ? InkWell(
                                          onTap: () => presenter.intentHandler(
                                            const PaymentPlansIntent
                                                .signUpPressed(),
                                          ),
                                          child: Center(
                                            child: TextWithHighlight(
                                              text: str.noAccountSignUp,
                                              highlightedText: str.signUpHere,
                                              style: TextStyles.titleSmall
                                                  .copyWith(
                                                      color: Palette.onSurface),
                                              highlightStyle: TextStyles
                                                  .titleSmall
                                                  .copyWith(
                                                      color: Palette.primary),
                                            ),
                                          ),
                                        )
                                      : InkWell(
                                          onTap: () => presenter.intentHandler(
                                            const PaymentPlansIntent
                                                .restorePressed(),
                                          ),
                                          child: Center(
                                            child: TextWithHighlight(
                                              text: str
                                                  .restorePurchaseDescription,
                                              highlightedText:
                                                  str.restorePurchase,
                                              style: TextStyles.titleSmall
                                                  .copyWith(
                                                      color: Palette.onSurface),
                                              highlightStyle: TextStyles
                                                  .titleSmall
                                                  .copyWith(
                                                      color: Palette.primary),
                                            ),
                                          ),
                                        ),
                                ],
                                const SizedBox(height: 16),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(BuildContext context, PaymentPlansPresenter presenter) {
    return SizedBox(
      height: 163,
      child: Stack(
        children: [
          Stack(
            children: [
              Container(
                height: 150,
                decoration: const BoxDecoration(color: Palette.yellowF7),
              ),
              Positioned(
                right: 16,
                child: IconButton(
                  onPressed: () => presenter
                      .intentHandler(const PaymentPlansIntent.closePressed()),
                  icon: const Icon(
                    Icons.close,
                    color: Colors.black,
                    size: 24,
                  ),
                ),
              ),
              Positioned(
                bottom: 20,
                left: 0,
                right: 0,
                child: Center(
                  child: SvgPicture.asset('assets/images/ic_cussme_legacy.svg'),
                ),
              ),
            ],
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 220,
            child: Center(
              child: Image.asset(
                'assets/images/plan_man.png',
                height: 85,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList(Str str) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 68),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildFeatureItem(
            'assets/images/ic_mega_speaker.png',
            str.paymentPlansAdFreeExperience,
          ),
          const Divider(height: 24, thickness: 1),
          _buildFeatureItem(
            'assets/images/ic_clipboard.png',
            str.paymentPlansFullWordList,
          ),
          const Divider(height: 24, thickness: 1),
          _buildFeatureItem(
            'assets/images/ic_speaker.png',
            str.paymentPlansSpeechPlaybackBeta,
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String iconPath, String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        Container(
          width: 24,
          height: 24,
          padding: const EdgeInsets.all(4),
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Palette.yellowD3,
          ),
          child: Image.asset(iconPath),
        ),
        const SizedBox(width: 18),
        Expanded(
          child: Text(
            text,
            style: TextStyles.titleSmall.copyWith(color: Palette.onSurface),
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentPlans(
      PaymentPlansState state, PaymentPlansPresenter presenter) {
    return Column(
      children: state.plans.map((plan) {
        final isSelected = state.selectedPlan == plan;
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: isSelected ? Palette.whiteEb : Colors.white,
            border: Border.all(
              color: isSelected ? Palette.primary : Palette.onSurface,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => presenter
                  .intentHandler(PaymentPlansIntent.planSelected(plan)),
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Radio(
                      value: true,
                      groupValue: isSelected,
                      onChanged: (_) => presenter
                          .intentHandler(PaymentPlansIntent.planSelected(plan)),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity:
                          const VisualDensity(horizontal: -4, vertical: -4),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                plan.title,
                                style: TextStyles.titleMedium.copyWith(
                                  color: isSelected
                                      ? Palette.primary
                                      : Palette.black1d,
                                  fontWeight: isSelected
                                      ? FontWeight.w600
                                      : FontWeight.w500,
                                ),
                              ),
                              if (plan.subtitle != null) ...[
                                const SizedBox(width: 8),
                                Text(
                                  plan.subtitle!,
                                  style: TextStyles.bodySmall.copyWith(
                                    color: isSelected
                                        ? Palette.primary
                                        : Palette.black1d,
                                    fontWeight: isSelected
                                        ? FontWeight.w600
                                        : FontWeight.w500,
                                  ),
                                ),
                              ],
                            ],
                          ),
                          if (plan.badge != null) ...[
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Palette.primary,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                plan.badge!,
                                style: TextStyles.labelMedium.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(paymentPlansSideEffectsProvider, (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case NavigateBackSideEffect _:
            Navigator.pop(context);
            break;
          case NavigateToHomeSideEffect _:
            GoRouter.of(context).goToHome();
            break;
          case final ShowErrorSideEffect intent:
            Toast.show(context, intent.message);
            break;
          case ShowSubscriptionSuccessDialogSideEffect _:
            SubscriptionSuccessDialog.show(context);
            break;
          case ShowRestoreSubscriptionDialogSideEffect _:
            RestoreSubscriptionDialog.show(context);
            break;
          case NavigateToSignUpSideEffect _:
            GoRouter.of(context).showAuthBottomSheet(showSignUp: true);
            break;
        }
      });
    });
  }
}
