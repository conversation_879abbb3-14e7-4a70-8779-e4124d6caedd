import 'dart:async';
import 'dart:io';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/routing/navigation_data.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../localization/generated/l10n.dart';
import 'contract/signin_contract.dart';

part 'signin_presenter.g.dart';

@riverpod
class SignInPresenter extends _$SignInPresenter {
  StreamController<SignInSideEffect> sideEffects =
      StreamController<SignInSideEffect>();
  late final AuthUseCase _authUseCase;

  @override
  SignInState build({NavigationData? navigationData}) {
    _authUseCase = ref.read(authUseCaseProvider);
    ref.onDispose(() => sideEffects.close());
    return SignInState(navigationData: navigationData);
  }

  void _hideKeyboard() {
    sideEffects.safeAdd(const SignInSideEffect.hideKeyboard());
  }

  void _navigateAfterAuth(UserEntity user) {
    if (state.navigationData != null) {
      sideEffects.safeAdd(SignInSideEffect.navigateToDestination(
        state.navigationData!.copyWith(user: user),
      ));
    } else {
      sideEffects.safeAdd(const SignInSideEffect.navigateToHome());
    }
  }

  void intentHandler(SignInIntent intent) {
    switch (intent) {
      case final EmailChangedIntent intent:
        state = state.copyWith(email: intent.email, emailError: null);
        break;
      case final PasswordChangedIntent intent:
        state = state.copyWith(password: intent.password, passwordError: null);
        break;
      case TogglePasswordVisibilityIntent _:
        state = state.copyWith(isPasswordVisible: !state.isPasswordVisible);
        break;
      case SignInWithEmailIntent _:
        _hideKeyboard();
        _signInWithEmail();
        break;
      case SignInWithGoogleIntent _:
        _hideKeyboard();
        _signInWithGoogle();
        break;
      case final SignInWithAppleIntent intent:
        _hideKeyboard();
        _signInWithApple(idToken: intent.idToken);
        break;
      case ForgotPasswordIntent _:
        sideEffects.safeAdd(const SignInSideEffect.navigateToForgotPassword());
        break;
      case NavigateToSignUpIntent _:
        _hideKeyboard();
        sideEffects.safeAdd(const SignInSideEffect.navigateToSignUp());
        break;
      case ContinueWithoutSignInIntent _:
        _hideKeyboard();
        _continueAsGuest();
        break;
      case OpenPrivacyPolicyIntent _:
        sideEffects.safeAdd(const SignInSideEffect.openUrl(privacyPolicyUrl));
        break;
      case OpenTermsAndConditionsIntent _:
        sideEffects
            .safeAdd(const SignInSideEffect.openUrl(termsAndConditionsUrl));
        break;
      case OpenEulaIntent _:
        sideEffects.safeAdd(const SignInSideEffect.openUrl(eulaUrl));
        break;
    }
  }

  bool _validateForm() {
    bool isValid = true;

    state = state.copyWith(
      emailError: null,
      passwordError: null,
    );

    final emailError = Validators.validateEmail(state.email);
    if (emailError != null) {
      state = state.copyWith(emailError: emailError);
      isValid = false;
    }

    final passwordError = Validators.validatePassword(state.password);
    if (passwordError != null) {
      state = state.copyWith(passwordError: passwordError);
      isValid = false;
    }

    if (!isValid) {
      sideEffects.safeAdd(
          SignInSideEffect.showError(Str.current.invalidInputsErrorMessage));
    }

    return isValid;
  }

  Future<void> _signInWithEmail() async {
    if (!_validateForm()) return;

    try {
      state = state.copyWith(isEmailLoading: true);

      final user = await _authUseCase.signInWithEmail(
        email: state.email.trim(),
        password: state.password,
      );

      _navigateAfterAuth(user);
    } on CussMeException catch (e) {
      sideEffects.safeAdd(SignInSideEffect.showError(e.message));
    } finally {
      state = state.copyWith(isEmailLoading: false);
    }
  }

  Future<void> _signInWithGoogle() async {
    try {
      state = state.copyWith(isGoogleLoading: true);

      final (user, profileExists) = await _authUseCase.signInWithGoogle();

      if (profileExists || state.navigationData != null) {
        _navigateAfterAuth(user);
      } else {
        sideEffects.safeAdd(const SignInSideEffect.navigateToIntro());
      }
    } on CussMeException catch (e) {
      state = state.copyWith(isGoogleLoading: false);
      sideEffects.safeAdd(SignInSideEffect.showError(e.message));
    }
  }

  Future<void> _signInWithApple({String? idToken}) async {
    try {
      if (Platform.isAndroid && idToken == null) {
        sideEffects.safeAdd(const SignInSideEffect.navigateToAppleWebView());
        return;
      }

      state = state.copyWith(isAppleLoading: true);

      final (user, profileExists) = await _authUseCase.signInWithApple(idToken);

      if (profileExists || state.navigationData != null) {
        _navigateAfterAuth(user);
      } else {
        sideEffects.safeAdd(const SignInSideEffect.navigateToIntro());
      }
    } on CussMeException catch (e) {
      state = state.copyWith(isAppleLoading: false);
      sideEffects.safeAdd(SignInSideEffect.showError(e.message));
    }
  }

  Future<void> _continueAsGuest() async {
    try {
      await _authUseCase.setGuest();

      sideEffects.safeAdd(const SignInSideEffect.navigateToGuestHome());
    } on CussMeException catch (e) {
      sideEffects.safeAdd(SignInSideEffect.showError(e.message));
    }
  }
}

@riverpod
Stream<SignInSideEffect> signInSideEffects(Ref ref,
    {NavigationData? navigationData}) {
  return ref
      .read(signInPresenterProvider(navigationData: navigationData).notifier)
      .sideEffects
      .stream;
}
