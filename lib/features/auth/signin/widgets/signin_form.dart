import 'package:cussme/routing/app_router.dart';
import 'package:cussme/routing/navigation_data.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import '../../../../localization/generated/l10n.dart';
import '../contract/signin_contract.dart';
import '../signin_presenter.dart';

class SignInForm extends ConsumerWidget {
  final NavigationData? navigationData;
  final VoidCallback? onSwitchToSignUp;
  final bool isBottomSheet;
  final ScrollController? scrollController;

  const SignInForm({
    super.key,
    this.navigationData,
    this.onSwitchToSignUp,
    this.isBottomSheet = false,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state =
        ref.watch(signInPresenterProvider(navigationData: navigationData));
    final presenter = ref
        .read(signInPresenterProvider(navigationData: navigationData).notifier);
    _handleSideEffects(context, ref);
    final str = Str.of(context);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          isBottomSheet
                              ? const SizedBox(
                                  height: 24,
                                )
                              : Column(
                                  children: [
                                    const SizedBox(height: 32),
                                    Center(
                                      child: SvgPicture.asset(
                                        'assets/images/splash_logo.svg',
                                        height: 75,
                                      ),
                                    ),
                                    const SizedBox(height: 17),
                                    Text(
                                      str.signInTitle,
                                      style: TextStyles.headlineSmall.copyWith(
                                        color: Palette.primary,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      str.signInSubtitle,
                                      style: TextStyles.bodyMedium.copyWith(
                                        color: Palette.onSurfaceVariant,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 18),
                                  ],
                                ),
                          const Spacer(),
                          CustomTextField(
                            label: str.emailLabel,
                            errorText: state.emailError,
                            onChanged: (value) => presenter.intentHandler(
                              SignInIntent.emailChanged(value),
                            ),
                            keyboardType: TextInputType.emailAddress,
                            textInputAction: TextInputAction.next,
                          ),
                          const SizedBox(height: 12),
                          CustomTextField(
                            label: str.passwordLabel,
                            errorText: state.passwordError,
                            obscureText: !state.isPasswordVisible,
                            togglePasswordVisibility: true,
                            onToggleVisibility: () => presenter.intentHandler(
                              const SignInIntent.togglePasswordVisibility(),
                            ),
                            onChanged: (value) => presenter.intentHandler(
                              SignInIntent.passwordChanged(value),
                            ),
                            textInputAction: TextInputAction.done,
                          ),
                          const SizedBox(height: 4),
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed: () => presenter.intentHandler(
                                const SignInIntent.forgotPassword(),
                              ),
                              style: TextButton.styleFrom(
                                foregroundColor: Palette.primaryDark,
                                textStyle: TextStyles.labelMedium,
                                padding: EdgeInsets.zero,
                                minimumSize: Size.zero,
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: Padding(
                                padding: const EdgeInsets.fromLTRB(8, 4, 0, 4),
                                child: Text(str.forgotPasswordButton),
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          PrimaryButton(
                            text: str.signInButton,
                            onPressed: () => presenter.intentHandler(
                              const SignInIntent.signInWithEmail(),
                            ),
                            isLoading: state.isEmailLoading,
                          ),
                          const SizedBox(height: 18),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Expanded(
                                  child: Divider(color: Palette.outline),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                  ),
                                  child: Text(
                                    str.orSignInWith,
                                    style: TextStyles.bodySmall.copyWith(
                                      color: Palette.onSurfaceVariant,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                const Expanded(
                                    child: Divider(color: Palette.outline)),
                              ],
                            ),
                          ),
                          const SizedBox(height: 18),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 20.0),
                            child: SocialAuthButtons(
                              onGooglePressed: () => presenter.intentHandler(
                                const SignInIntent.signInWithGoogle(),
                              ),
                              onApplePressed: () => presenter.intentHandler(
                                const SignInIntent.signInWithApple(),
                              ),
                              isGoogleLoading: state.isGoogleLoading,
                              isAppleLoading: state.isAppleLoading,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  str.noAccountText,
                                  style: TextStyles.labelLarge.copyWith(
                                    color: Palette.primaryDark,
                                  ),
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () => presenter.intentHandler(
                                    const SignInIntent.navigateToSignUp(),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8.0, horizontal: 2.0),
                                    child: Text(
                                      str.signUpHereText,
                                      style: TextStyles.labelLarge.copyWith(
                                        color: Palette.primary,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              GestureDetector(
                                onTap: () => presenter.intentHandler(
                                  const SignInIntent.openPrivacyPolicy(),
                                ),
                                child: Text(
                                  Str.of(context).privacyPolicy,
                                  style: TextStyles.labelLarge.copyWith(
                                      color: Palette.onSurfaceVariant),
                                ),
                              ),
                              Text(Str.of(context).linkSeparator,
                                  style: TextStyles.labelLarge.copyWith(
                                      color: Palette.onSurfaceVariant)),
                              GestureDetector(
                                onTap: () => presenter.intentHandler(
                                  const SignInIntent.openTermsAndConditions(),
                                ),
                                child: Text(
                                  Str.of(context).termsAndConditions,
                                  style: TextStyles.labelLarge.copyWith(
                                      color: Palette.onSurfaceVariant),
                                ),
                              ),
                              Text(Str.of(context).linkSeparator,
                                  style: TextStyles.labelLarge.copyWith(
                                      color: Palette.onSurfaceVariant)),
                              GestureDetector(
                                onTap: () => presenter.intentHandler(
                                  const SignInIntent.openEula(),
                                ),
                                child: Text(
                                  Str.of(context).eula,
                                  style: TextStyles.labelLarge.copyWith(
                                      color: Palette.onSurfaceVariant),
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                        ],
                      ),
                    ),
                  ),
                  isBottomSheet
                      ? const SizedBox(height: 24)
                      : Padding(
                          padding: const EdgeInsets.fromLTRB(0, 16, 16.0, 20.0),
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed: () => presenter.intentHandler(
                                const SignInIntent.continueWithoutSignIn(),
                              ),
                              style: TextButton.styleFrom(
                                foregroundColor: Palette.primary,
                                padding: EdgeInsets.zero,
                                visualDensity: VisualDensity.compact,
                                minimumSize: Size.zero,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    str.continueWithoutSignIn,
                                    style: TextStyles.labelLarge,
                                  ),
                                  const SizedBox(width: 4),
                                  const Icon(Icons.keyboard_arrow_right,
                                      size: 24),
                                ],
                              ),
                            ),
                          ),
                        ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(signInSideEffectsProvider(navigationData: navigationData),
        (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case NavigateToHomeSideEffect _:
            if (isBottomSheet) Navigator.of(context).pop();
            GoRouter.of(context).goToHome();
            break;
          case NavigateToGuestHomeSideEffect _:
            if (isBottomSheet) Navigator.of(context).pop();
            GoRouter.of(context).goToGuestHome();
            break;
          case NavigateToSignUpSideEffect _:
            if (isBottomSheet) {
              onSwitchToSignUp!();
            } else {
              GoRouter.of(context).pushToSignUp(navigationData: navigationData);
            }
            break;
          case NavigateToForgotPasswordSideEffect _:
            if (isBottomSheet) Navigator.of(context).pop();
            GoRouter.of(context).pushToForgotPassword();
            break;
          case NavigateToIntroSideEffect _:
            if (isBottomSheet) Navigator.of(context).pop();
            GoRouter.of(context).goToIntro();
            break;
          case final NavigateToIntroWithDestinationSideEffect sideEffect:
            if (isBottomSheet) Navigator.of(context).pop();
            GoRouter.of(context)
                .goToIntro(navigationData: sideEffect.navigationData);
            break;
          case final NavigateToDestinationSideEffect sideEffect:
            if (isBottomSheet) Navigator.of(context).pop();
            GoRouter.of(context)
                .goToDestinationWithHomeInStack(sideEffect.navigationData);
            break;
          case final ShowErrorSideEffect intent:
            Toast.show(context, intent.message);
            break;
          case HideKeyboardSideEffect _:
            KeyboardUtils.hideKeyboard(context);
            break;
          case NavigateToAppleWebViewSideEffect _:
            GoRouter.of(context)
                .pushToAppleWebView(navigationData: navigationData)
                .then((idToken) {
              if (idToken != null) {
                final presenter = ref.read(
                  signInPresenterProvider(navigationData: navigationData)
                      .notifier,
                );
                presenter.intentHandler(
                    SignInIntent.signInWithApple(idToken: idToken));
              }
            });
            break;
          case final OpenUrlSideEffect intent:
            openInBrowser(intent.url);
            break;
        }
      });
    });
  }
}
