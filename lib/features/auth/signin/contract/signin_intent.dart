import 'package:freezed_annotation/freezed_annotation.dart';

part 'signin_intent.freezed.dart';

@freezed
sealed class SignInIntent with _$SignInIntent {
  const factory SignInIntent.emailChanged(String email) = EmailChangedIntent;
  const factory SignInIntent.passwordChanged(String password) =
      PasswordChangedIntent;
  const factory SignInIntent.togglePasswordVisibility() =
      TogglePasswordVisibilityIntent;
  const factory SignInIntent.signInWithEmail() = SignInWithEmailIntent;
  const factory SignInIntent.signInWithGoogle() = SignInWithGoogleIntent;
  const factory SignInIntent.signInWithApple({String? idToken}) =
      SignInWithAppleIntent;
  const factory SignInIntent.forgotPassword() = ForgotPasswordIntent;
  const factory SignInIntent.navigateToSignUp() = NavigateToSignUpIntent;
  const factory SignInIntent.continueWithoutSignIn() =
      ContinueWithoutSignInIntent;
  const factory SignInIntent.openPrivacyPolicy() = OpenPrivacyPolicyIntent;
  const factory SignInIntent.openTermsAndConditions() =
      OpenTermsAndConditionsIntent;
  const factory SignInIntent.openEula() = OpenEulaIntent;
}
