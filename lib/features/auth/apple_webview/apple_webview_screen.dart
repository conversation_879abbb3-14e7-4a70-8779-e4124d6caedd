import 'package:cussme/data/data.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/routing/navigation_data.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AppleWebViewScreen extends StatefulWidget {
  final NavigationData? navigationData;

  const AppleWebViewScreen({super.key, this.navigationData});

  @override
  State<AppleWebViewScreen> createState() => _AppleWebViewScreenState();
}

class _AppleWebViewScreenState extends State<AppleWebViewScreen> {
  late WebViewController _webViewController;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
    WidgetsBinding.instance.addPostFrameCallback((_) => _loadInitialUrl());
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith(appleSignInRedirectUrl)) {
              _handleAppleRedirect(request.url);
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
          onPageFinished: (String url) {
            if (url.startsWith(appleSignInRedirectUrl)) {
              _handleAppleRedirect(url);
            }
          },
        ),
      );
  }

  void _handleAppleRedirect(String url) {
    try {
      final fragmentParams = Uri.splitQueryString(url);
      final idToken = fragmentParams['id_token'];

      if (idToken != null) {
        context.pop(idToken);
      } else {
        context.pop();
      }
    } catch (e) {
      context.pop();
    }
  }

  void _loadInitialUrl() {
    _webViewController.loadRequest(Uri.parse(_getAppleSignInUrl()));
  }

  String _getAppleSignInUrl() {
    const clientId = 'xyz.cussme.auth';
    final redirectUri = Uri.encodeComponent(appleSignInRedirectUrl);
    const responseType = 'code id_token';
    const responseMode = 'fragment';

    return 'https://appleid.apple.com/auth/authorize'
        '?client_id=$clientId'
        '&redirect_uri=$redirectUri'
        '&response_type=$responseType'
        '&response_mode=$responseMode';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Str.of(context).signInApple),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => context.pop(),
        ),
      ),
      body: WebViewWidget(controller: _webViewController),
    );
  }
}
