import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/routing/navigation_data.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'signin/signin_bottom_sheet_screen.dart';
import 'signup/signup_bottom_sheet_screen.dart';

class AuthBottomSheetModal extends ConsumerStatefulWidget {
  AuthBottomSheetModal(
      {super.key, this.showSignUp = false, required this.navigationData});

  bool showSignUp;
  final NavigationData navigationData;

  @override
  ConsumerState<AuthBottomSheetModal> createState() =>
      _AuthBottomSheetModalState();
}

class _AuthBottomSheetModalState extends ConsumerState<AuthBottomSheetModal> {
  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.9,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Palette.surface,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 16, 16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        Str.of(context).signInRequired,
                        style: TextStyles.titleLarge.copyWith(
                          color: Palette.onSurface,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    SizedBox(
                      width: 36,
                      height: 36,
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        icon: const Icon(Icons.close),
                        color: Palette.onSurface,
                        iconSize: 24,
                        constraints: const BoxConstraints(),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  Str.of(context).signInRequiredDescription,
                  style:
                      TextStyles.bodyMedium.copyWith(color: Palette.onSurface),
                ),
              ),
              Expanded(
                child: widget.showSignUp
                    ? SignUpBottomSheetContent(
                        navigationData: widget.navigationData,
                        onSwitchToSignIn: () =>
                            setState(() => widget.showSignUp = false),
                        scrollController: scrollController,
                      )
                    : SignInBottomSheetContent(
                        navigationData: widget.navigationData,
                        onSwitchToSignUp: () =>
                            setState(() => widget.showSignUp = true),
                        scrollController: scrollController,
                      ),
              ),
            ],
          ),
        );
      },
    );
  }
}
