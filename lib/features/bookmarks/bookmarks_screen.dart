import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'bookmarks_presenter.dart';
import 'contract/bookmarks_contract.dart';

class BookmarksScreen extends ConsumerStatefulWidget {
  const BookmarksScreen({super.key});

  @override
  ConsumerState<BookmarksScreen> createState() => _BookmarksScreenState();
}

class _BookmarksScreenState extends ConsumerState<BookmarksScreen>
    with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.unsubscribe(this);
    routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final presenter =
          ref.read(bookmarksPresenterProvider(key: widget.key!).notifier);
      presenter.intentHandler(const BookmarksIntent.returned());
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(bookmarksPresenterProvider(key: widget.key!));
    final presenter =
        ref.read(bookmarksPresenterProvider(key: widget.key!).notifier);
    _handleSideEffects(context);

    return AdMobScaffold(
      body: Column(
        children: [
          CussMeHeader(
            title: Str.of(context).bookmarksTitle,
            titleIcon: Icons.bookmark,
            backButtonText: Str.of(context).backButtonText,
            onBackPressed: () => presenter.intentHandler(
              const BookmarksIntent.goBack(),
            ),
          ),
          Expanded(
            child: state.loadingState == ScreenLoadingState.loaded
                ? _buildBookmarksList(context, state, presenter)
                : ScreenLoading(
                    state: state.loadingState,
                    onRetry: () =>
                        presenter.intentHandler(const BookmarksIntent.retry()),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookmarksList(
    BuildContext context,
    BookmarksState state,
    BookmarksPresenter presenter,
  ) {
    if (state.groupedBookmarkItems.isEmpty) {
      return Center(
        child: Text(
          Str.of(context).bookmarksEmptyMessage,
          style: TextStyles.bodyLarge.copyWith(color: Palette.onSurface),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 8),
      itemCount: state.groupedBookmarkItems.length,
      itemBuilder: (context, index) {
        final item = state.groupedBookmarkItems[index];

        if (item is ListItemHeader) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            child: Text(
              item.header,
              style: TextStyles.titleLarge.copyWith(
                color: Palette.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        } else if (item is ListItemWord) {
          return WordListCardItem(
            word: item.word,
            onTap: () => presenter.intentHandler(
              BookmarksIntent.navigateToWordDetail(item.word.id),
            ),
            onBookmarkTap: () => presenter.intentHandler(
              BookmarksIntent.removeBookmark(item.word.id),
            ),
            onPlayPronunciation: () =>
                presenter.intentHandler(PlayPronunciationIntent(item.word)),
          );
        } else if (item is ListItemAd) {
          return ResponsiveBannerAdWidget(
            key: ValueKey(item.key),
            padding: const EdgeInsets.symmetric(vertical: 16),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  void _handleSideEffects(BuildContext context) {
    ref.listen(bookmarksSideEffectsProvider(key: widget.key!), (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case ShowMessageSideEffect _:
            Toast.show(context, sideEffect.message);
            break;
          case GoBackSideEffect _:
            Navigator.of(context).pop();
            break;
          case final NavigateToWordDetailSideEffect se:
            GoRouter.of(context).pushToWordDetail(wordId: se.wordId);
            break;
          case NavigateToPremiumSideEffect _:
            GoRouter.of(context).pushToPremium();
            break;
        }
      });
    });
  }
}
