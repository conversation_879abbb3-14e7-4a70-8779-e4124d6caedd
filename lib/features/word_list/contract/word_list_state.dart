import 'package:cussme/domain/domain.dart';
import 'package:cussme/ui/ui.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'word_list_state.freezed.dart';

@freezed
sealed class WordListState with _$WordListState {
  const WordListState._();

  const factory WordListState({
    required LanguageEntity language,
    @Default([]) List<Spiciness> selectedSpiciness,
    @Default([]) List<ListItem> groupedWordItems,
    @Default(ScreenLoadingState.loading) ScreenLoadingState loadingState,
    @Default(false) bool isInitialLoading,
    @Default(false) bool isGuest,
    @Default(false) bool showStickyWarning,
    UserEntity? user,
    @Default(false) bool isPremium,
  }) = _WordListState;

  WordListState updateBookmarkByWordId(String wordId, bool isBookmarked) =>
      copyWith(
        groupedWordItems: groupedWordItems.map((item) {
          if (item is ListItemWord && item.word.id == wordId) {
            return item.copyWith(
              word: item.word.copyWith(isBookmarked: isBookmarked),
            );
          }
          return item;
        }).toList(),
      );
  bool get hasPremiumWarning =>
      groupedWordItems.any((item) => item is ListItemPremiumWarning);
}
