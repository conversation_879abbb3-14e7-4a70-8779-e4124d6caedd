// ignore: depend_on_referenced_packages
import 'package:async/async.dart';
import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'word_use_case.g.dart';

class WordUseCase {
  final WordRepository _wordRepository;
  final AuthUseCase _authUseCase;
  final TTSService _ttsService;
  final SubscriptionUseCase _subscriptionUseCase;
  CancelableOperation? _filteredWordsOperation;
  CancelableOperation? _searchOperation;

  WordUseCase(this._wordRepository, this._authUseCase, this._ttsService,
      this._subscriptionUseCase);

  Future<WordEntity> getWordById(String wordId) async {
    final isGuest = _authUseCase.isGuest();
    return await _wordRepository.getWordById(
      wordId: wordId,
      isGuest: isGuest,
    );
  }

  Future<HomeResponse> getHome() async {
    final isGuest = _authUseCase.isGuest();

    List<SubscriptionEntity> purchases = [];
    try {
      purchases = await _subscriptionUseCase.restorePurchasesAndGet();
    } catch (e) {
      purchases = [];
    }
    final request =
        SubscriptionVerificationRequest(purchases: purchases.toList());
    final response =
        await _wordRepository.getHome(isGuest: isGuest, request: request);

    _authUseCase.updatePremium(response.isPremium);

    return response;
  }

  Future<bool> toggleBookmark(String wordId, bool currentStatus) async {
    final isGuest = _authUseCase.isGuest();

    if (isGuest) {
      throw CussMeException(Str.current.guestBookmarkError);
    }

    final user = _authUseCase.getCurrentUser();
    if (user == null) {
      throw CussMeException(Str.current.userNotLoggedIn);
    }

    return await _wordRepository.toggleBookmark(
      wordId: wordId,
      profileId: user.id,
      currentStatus: currentStatus,
    );
  }

  Future<bool> submitWordSuggestion({
    required String wordId,
    required String suggestion,
    required Spiciness spiciness,
  }) async {
    final wordSuggestion = WordSuggestionRequest(
      wordId: wordId,
      suggestion: suggestion,
      spiciness: spiciness,
    );

    return await _wordRepository.submitWordSuggestion(
        suggestion: wordSuggestion);
  }

  Future<bool> submitWordReport({
    required String wordId,
    required ReportReason reason,
    String? detail,
  }) async {
    final wordReport = WordReportRequest(
      wordId: wordId,
      reason: reason.name,
      detail: detail,
    );

    return await _wordRepository.submitWordReport(report: wordReport);
  }

  Future<List<ListItem>> search({required String query}) async {
    await _searchOperation?.cancel();

    final isPremium = _authUseCase.isPremium();
    _searchOperation = CancelableOperation.fromFuture(
      _wordRepository.search(query: query).then((response) {
        final List<ListItem> groupedItems = [];
        if (response.languages.isNotEmpty) {
          groupedItems.add(ListItem.categoryItem(Str.current.languages));
          groupedItems.addAll(
            response.languages
                .map((language) => ListItem.languageItem(language))
                .toList(),
          );
        }

        if (response.words.isNotEmpty) {
          groupedItems.add(ListItem.categoryItem(Str.current.words));

          String? currentGroup;
          for (final word in response.words) {
            final groupValue = word.language.name;

            if (currentGroup != groupValue) {
              groupedItems.add(ListItem.headerItem(groupValue));
              currentGroup = groupValue;
            }
            groupedItems.add(
              isPremium || word.isFree
                  ? ListItem.wordItem(word)
                  : ListItem.blurredWordItem(word),
            );
          }
        }
        return groupedItems;
      }),
    );

    return await _searchOperation?.value;
  }

  Future<List<WordEntity>> _filterWords(
    String languageId,
    List<Spiciness> spiciness,
  ) async {
    if (languageId.isEmpty || spiciness.isEmpty) {
      ExceptionHandler.handleError(Str.current.somethingWentWrong);
    }

    final request = FilterWordsRequest(
      languageId: languageId,
      spiciness: spiciness,
    );

    final isAuthenticated = !_authUseCase.isGuest();

    return await _wordRepository.filterWords(
      request: request,
      isAuthenticated: isAuthenticated,
    );
  }

  Future<List<ListItem>> getGroupedFilteredWords({
    required String languageId,
    required List<Spiciness> spiciness,
  }) async {
    try {
      await _filteredWordsOperation?.cancel();

      _filteredWordsOperation = CancelableOperation.fromFuture(
        _filterWords(languageId, spiciness).then((filteredWords) {
          final isPremium = _authUseCase.isPremium();

          final groupedItems = _createGroupedItems(
            filteredWords,
            getHeaderKey: (word) => word.word.firstLetter,
            isFreeUser: !isPremium,
          );

          return groupedItems;
        }),
      );

      return await _filteredWordsOperation?.value;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  Future<List<WordEntity>> _getBookmarks() async {
    final isGuest = _authUseCase.isGuest();
    if (isGuest) {
      throw CussMeException(Str.current.guestBookmarkError);
    }

    final user = _authUseCase.getCurrentUser();
    if (user == null) {
      throw CussMeException(Str.current.userNotLoggedIn);
    }

    return await _wordRepository.getBookmarks(userId: user.id);
  }

  Future<List<ListItem>> getGroupedBookmarks() async {
    final bookmarks = await _getBookmarks();
    return _createGroupedItems(
      bookmarks,
      getHeaderKey: (word) => word.language.name,
    );
  }

  List<ListItem> _createGroupedItems(
    List<WordEntity> words, {
    required String Function(WordEntity) getHeaderKey,
    bool isFreeUser = false,
  }) {
    final List<ListItem> items = [];
    String? currentGroup;
    int wordCount = 1;
    WordEntity? previousWord;

    for (final word in words) {
      final groupValue = getHeaderKey(word);

      if (isFreeUser) {
        if (currentGroup != groupValue && word.isFree) {
          items.add(ListItem.headerItem(groupValue));
          currentGroup = groupValue;
        }

        if (word.isFree) {
          items.add(ListItem.wordItem(word));
          if (wordCount % 5 == 0) {
            items.add(ListItem.adItem('${word.id}_ad_$wordCount'));
          }
        } else if (previousWord?.isFree == true) {
          items.add(ListItem.premiumWarningItem(word));
        } else {
          items.add(ListItem.blurredWordItem(word));
        }
      } else {
        if (currentGroup != groupValue) {
          items.add(ListItem.headerItem(groupValue));
          currentGroup = groupValue;
        }
        items.add(ListItem.wordItem(word));
      }

      wordCount++;
      previousWord = word;
    }

    return items;
  }

  Future<void> speak(WordEntity word) async {
    final isPremium = _authUseCase.isPremium();

    if (!isPremium) {
      final isGuest = _authUseCase.isGuest();
      throw PremiumRequiredException(isGuest);
    }

    await _ttsService.speak(word.word, word.language.locale);
  }
}

@riverpod
WordUseCase wordUseCase(Ref ref) {
  final wordRepository = ref.read(wordRepositoryProvider);
  final authUseCase = ref.read(authUseCaseProvider);
  final ttsService = ref.read(ttsServiceProvider);
  final subscriptionUseCase = ref.read(subscriptionUseCaseProvider);
  return WordUseCase(
      wordRepository, authUseCase, ttsService, subscriptionUseCase);
}
