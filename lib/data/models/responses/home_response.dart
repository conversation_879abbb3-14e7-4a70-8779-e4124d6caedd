import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../domain/entities/language_entity.dart';
import '../../../domain/entities/word_entity.dart';

part 'home_response.freezed.dart';
part 'home_response.g.dart';

@Freezed(toJson: true)
abstract class HomeResponse with _$HomeResponse {
  const factory HomeResponse({
    required List<LanguageEntity> languages,
    required List<WordEntity> words,
    required bool selectedAllLanguages,
    required bool isPremium,
  }) = _HomeResponse;

  factory HomeResponse.fromJson(Map<String, dynamic> json) =>
      _$HomeResponseFromJson(json);
}
