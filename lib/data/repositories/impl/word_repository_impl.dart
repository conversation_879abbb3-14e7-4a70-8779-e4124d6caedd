import 'dart:convert';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/utils/utils.dart';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';

class WordRepositoryImpl implements WordRepository {
  final SupabaseClient _supabaseClient;

  WordRepositoryImpl({
    required SupabaseClient supabaseClient,
  }) : _supabaseClient = supabaseClient;

  @override
  Future<List<WordEntity>> searchWords({
    required String languageId,
    required String query,
    List<Spiciness>? spiciness,
  }) async {
    try {
      const table = SupabaseTable.words;
      final columns = table.columns as WordColumns;

      var queryBuilder = _supabaseClient
          .from(table.name)
          .select()
          .eq(columns.language, languageId)
          .ilike(columns.word, '%$query%');

      if (spiciness != null && spiciness.isNotEmpty) {
        final spicinessValues = spiciness.map((s) => s.name).toList();
        queryBuilder =
            queryBuilder.inFilter(columns.spiciness, spicinessValues);
      }

      final response = await queryBuilder.order(columns.word);

      return response.toWordEntityList();
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<List<WordEntity>> getWords({
    required String languageId,
    List<Spiciness>? spiciness,
  }) async {
    try {
      const table = SupabaseTable.words;
      final columns = table.columns as WordColumns;

      var queryBuilder = _supabaseClient
          .from(table.name)
          .select()
          .eq(columns.language, languageId);

      if (spiciness != null && spiciness.isNotEmpty) {
        final spicinessValues = spiciness.map((s) => s.name).toList();
        queryBuilder =
            queryBuilder.inFilter(columns.spiciness, spicinessValues);
      }

      final response = await queryBuilder.order(columns.word);

      return response.toWordEntityList();
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<HomeResponse> getHome({
    required bool isGuest,
    required SubscriptionVerificationRequest request,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(homeFunctionUrl),
        headers: {
          'Content-Type': 'application/json',
          if (!isGuest)
            'Authorization':
                _supabaseClient.auth.currentSession!.accessToken.bearer,
        },
        body: jsonEncode(request.toJson()),
      );

      final data = json.decode(utf8.decode(response.bodyBytes));
      return HomeResponse.fromJson(data);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<WordEntity> getWordById(
      {required String wordId, required bool isGuest}) async {
    try {
      final response = await http.post(
        Uri.parse(wordByIdFunctionUrl),
        headers: {
          'Content-Type': 'application/json',
          if (!isGuest)
            'Authorization':
                'Bearer ${_supabaseClient.auth.currentSession!.accessToken}',
        },
        body: jsonEncode({'id': wordId}),
      );

      final data = json.decode(utf8.decode(response.bodyBytes));
      return WordEntity.fromJson(data);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<bool> toggleBookmark({
    required String wordId,
    required String profileId,
    required bool currentStatus,
  }) async {
    try {
      const table = SupabaseTable.bookmarks;
      final columns = table.columns as BookmarkColumns;

      if (currentStatus) {
        await _supabaseClient
            .from(table.name)
            .delete()
            .eq(columns.profileId, profileId)
            .eq(columns.wordId, wordId);
        return false;
      } else {
        await _supabaseClient.from(table.name).upsert({
          columns.profileId: profileId,
          columns.wordId: wordId,
        });
        return true;
      }
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<bool> isBookmarked({
    required String wordId,
    required String profileId,
  }) async {
    try {
      const table = SupabaseTable.bookmarks;
      final columns = table.columns as BookmarkColumns;

      final response = await _supabaseClient
          .from(table.name)
          .select()
          .eq(columns.profileId, profileId)
          .eq(columns.wordId, wordId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<bool> submitWordSuggestion({
    required WordSuggestionRequest suggestion,
  }) async {
    try {
      const table = SupabaseTable.wordSuggestions;
      final columns = table.columns as WordSuggestionColumns;

      final currentUser = _supabaseClient.auth.currentUser;
      final profileId = currentUser?.id;

      final data = {
        columns.wordId: suggestion.wordId,
        columns.suggestion: suggestion.suggestion,
        columns.spiciness: suggestion.spiciness.name,
        if (profileId != null) columns.profileId: profileId,
      };

      await _supabaseClient.from(table.name).insert(data);
      return true;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<bool> submitWordReport({
    required WordReportRequest report,
  }) async {
    try {
      const table = SupabaseTable.wordReports;
      final columns = table.columns as WordReportColumns;

      final currentUser = _supabaseClient.auth.currentUser;
      final profileId = currentUser?.id;

      final data = {
        columns.wordId: report.wordId,
        columns.reason: report.reason,
        if (report.detail != null && report.detail!.isNotEmpty)
          columns.detail: report.detail,
        if (profileId != null) columns.profileId: profileId,
      };

      await _supabaseClient.from(table.name).insert(data);
      return true;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<SearchResponse> search({required String query}) async {
    try {
      final response = await http.post(
        Uri.parse(searchFunctionUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({'query': query}),
      );

      final data = json.decode(utf8.decode(response.bodyBytes));
      return SearchResponse.fromJson(data);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<List<WordEntity>> filterWords({
    required FilterWordsRequest request,
    required bool isAuthenticated,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(filterWordsFunctionUrl),
        headers: {
          'Content-Type': 'application/json',
          if (isAuthenticated)
            'Authorization':
                _supabaseClient.auth.currentSession!.accessToken.bearer,
        },
        body: jsonEncode(request.toJson()),
      );

      final data = json.decode(utf8.decode(response.bodyBytes));
      return WordEntity.fromJsonList(data);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<List<WordEntity>> getBookmarks({
    required String userId,
  }) async {
    try {
      final response = await _supabaseClient.getBookmarkedWords(userId);
      return WordEntity.fromJsonList(response);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }
}
