import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/utils/utils.dart';
import 'package:http/http.dart' as http;
import 'package:in_app_purchase/in_app_purchase.dart';

import 'package:supabase_flutter/supabase_flutter.dart';

class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final InAppPurchase _inAppPurchase;
  final SupabaseClient _supabaseClient;
  final _purchaseResultStreamController =
      StreamController<PurchaseResult>.broadcast();

  StreamSubscription<List<PurchaseDetails>>? _purchaseStreamSubscription;
  bool isRestore = false;
  bool _isDisposed = false;

  Timer? _delayTimer;
  final Set<SubscriptionEntity> _accumulatedPurchases = {};

  SubscriptionRepositoryImpl(this._inAppPurchase, this._supabaseClient) {
    _purchaseStreamSubscription =
        _inAppPurchase.purchaseStream.listen(_handlePurchaseUpdates);
  }

  @override
  void dispose() {
    if (_isDisposed) return;
    _isDisposed = true;
    _delayTimer?.cancel();
    _purchaseStreamSubscription?.cancel();
    _purchaseResultStreamController.close();
  }

  @override
  Stream<PurchaseResult> get purchaseResultStream =>
      _purchaseResultStreamController.stream;

  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    if (_isDisposed) return;

    if (!isRestore) {
      final lastPurchase = purchaseDetailsList.last;
      if (lastPurchase.status == PurchaseStatus.error) {
        _cancelDelayAndClearAccumulated();
        _purchaseResultStreamController.add(
          PurchaseResult.error(message: lastPurchase.error!.message),
        );
        return;
      } else if (lastPurchase.status == PurchaseStatus.canceled) {
        _cancelDelayAndClearAccumulated();
        _purchaseResultStreamController.add(const PurchaseResult.cancelled());
        return;
      }
    }

    for (final purchaseDetails in purchaseDetailsList) {
      final purchaseToken = Platform.isIOS
          ? purchaseDetails.verificationData.localVerificationData
          : purchaseDetails.verificationData.serverVerificationData;

      final subscriptionEntity = SubscriptionEntity(
        productId: purchaseDetails.productID,
        platform: getPlatform(),
        purchaseToken: purchaseToken,
        purchaseId: purchaseDetails.purchaseID,
      );

      _accumulatedPurchases.add(subscriptionEntity);

      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }

    _cancelDelayAndStartNew();
  }

  void _cancelDelayAndClearAccumulated() {
    _delayTimer?.cancel();
    _accumulatedPurchases.clear();
  }

  void _cancelDelayAndStartNew() {
    _delayTimer?.cancel();
    _delayTimer = Timer(const Duration(milliseconds: 1500), () {
      if (!_isDisposed) {
        _emitSuccessResult();
      }
    });
  }

  void _emitSuccessResult() {
    _delayTimer?.cancel();

    if (_accumulatedPurchases.isEmpty) {
      _purchaseResultStreamController
          .add(PurchaseResult.error(message: Str.current.noPurchaseFound));
      return;
    }

    final purchasesToEmit = _accumulatedPurchases.toList();
    _accumulatedPurchases.clear();

    _purchaseResultStreamController.add(
      PurchaseResult.success(purchases: purchasesToEmit),
    );
  }

  @override
  Future<ProductDetails> getProductDetail(String productId) async {
    try {
      final ProductDetailsResponse response =
          await _inAppPurchase.queryProductDetails({productId});

      if (response.error != null || response.productDetails.isEmpty) {
        throw ExceptionHandler.handleError(response.error!.message);
      }

      return response.productDetails.first;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> purchaseProduct(PurchaseParam purchaseParam) async {
    isRestore = false;

    try {
      _cancelDelayAndClearAccumulated();
      final bool success =
          await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);

      if (!success) {
        throw ExceptionHandler.handleError(
            Str.current.failedToInitiatePurchase);
      }
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> restorePurchases() async {
    isRestore = true;
    try {
      _cancelDelayAndClearAccumulated();
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<bool> isInAppPurchaseAvailable() async {
    return await _inAppPurchase.isAvailable();
  }

  @override
  Future<SubscriptionVerificationResponse?> verifyAndSubmit(
      SubscriptionVerificationRequest request) async {
    try {
      final response = await http.post(
        Uri.parse(verifyAndSubmitPurchaseFunctionUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': supabaseAnonKey.bearer,
        },
        body: jsonEncode(request.toJson()),
      );

      return SubscriptionVerificationResponse.fromJson(
          jsonDecode(response.body));
    } catch (_) {
      return null;
    }
  }
}
