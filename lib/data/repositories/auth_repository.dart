import 'package:cussme/domain/domain.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../di/general_provider.dart';
import 'impl/auth_repository_impl.dart';

part 'auth_repository.g.dart';

abstract class AuthRepository {
  Future<User> signUpWithEmailPassword({
    required String email,
    required String password,
  });

  Future<User> signInWithEmailPassword({
    required String email,
    required String password,
  });

  Future<void> upsertProfile({
    required User user,
    required String firstName,
    String? lastName,
  });

  Future<void> deleteCurrentUser();

  Future<void> signOutSupabase();

  Future<void> signOutGoogle();

  Future<void> signOutFacebook();

  Future<void> saveUserToSharedPreferences(UserEntity user);

  UserEntity? getUserFromSharedPreferences();

  Future<void> clearSharedPreferences();

  Future<AuthorizationCredentialAppleID> getAppleCredential();

  Future<void> signUpWithAppleIdToken(String idToken);

  Future<GoogleSignInAccount> getGoogleSignInAccount();

  Future<void> signUpWithGoogle(GoogleSignInAccount googleSignInAccount);

  Future<String> getFacebookAccessToken();

  Future<void> signUpWithFacebook(String fbAccessToken);

  Future<void> insertCurrentUserProfile();

  Future<UserEntity> getProfile();

  Future<void> sendPasswordResetEmail(String email);

  Future<bool> checkIfEmailExists(String email);

  Future<void> verifyRecoveryOTP(String token);

  Future<void> updatePassword(String newPassword);

  Future<void> updateProfile(String firstName, String? lastName);

  Future<void> updateSpiciness(List<Spiciness> spiciness);

  Future<void> updateLanguages(List<LanguageEntity> languages);

  Future<List<LanguageEntity>> getAllLanguages();

  bool isGuest();

  Future<void> setGuest();

  Future<void> removeGuest();

  Future<void> syncCurrentUserToSupabase();

  User? getCurrentSupabaseUser();

  Future<bool> checkIfProfileExists();

  Future<void> updatePremium(bool isPremium);

  bool getPremiumSharedPreferences();
}

@riverpod
AuthRepository authRepository(Ref ref) {
  final supabaseClient = ref.read(supabaseClientProvider);
  final googleSignIn = ref.read(googleSignInProvider);
  final facebookAuth = ref.read(facebookAuthProvider);
  final sharedPreferences = ref.read(sharedPreferencesProvider);

  return AuthRepositoryImpl(
    supabaseClient: supabaseClient,
    googleSignIn: googleSignIn,
    facebookAuth: facebookAuth,
    sharedPreferences: sharedPreferences,
  );
}
