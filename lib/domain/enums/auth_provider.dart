enum AuthProvider {
  email,
  google,
  facebook,
  apple;

  bool get isEmail => this == AuthProvider.email;
  bool get isGoogle => this == AuthProvider.google;
  bool get isFacebook => this == AuthProvider.facebook;
  bool get isApple => this == AuthProvider.apple;

  static AuthProvider byName(String? name) {
    try {
      return AuthProvider.values.byName(name!);
    } catch (_) {
      return AuthProvider.email;
    }
  }
}
